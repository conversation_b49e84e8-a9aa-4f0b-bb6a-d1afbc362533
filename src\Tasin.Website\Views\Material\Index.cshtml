﻿@{
    ViewData["Title"] = "Nguyên liệu";
}

@section Styles {
    <link href="~/css/kendo-grid-common.css" rel="stylesheet" />
    <link href="~/css/toolbar-common.css" rel="stylesheet" />
}

<div>

    <h4 class="demo-section wide title">@ViewData["Title"]</h4>
    @*   <div class="demo-section wide title">
    <h4 class="demo-section wide title">@ViewData["Title"]</h4>
    <nav id="breadcrumb"></nav>
    </div> *@
    <div id="divContent">
        <div id="gridId">
        </div>
    </div>
    <div id="window"></div>
    <div id="dialog"></div>
</div>
<script type="text/x-kendo-template" id="template_form_header">
    <div style="width:100%">
        <button id='create' class='k-button k-button-md k-rounded-md k-button-solid k-button-solid-base _permission_' data-enum='6' style='margin-right: 5px;'><span class='k-icon k-i-plus k-button-icon'></span><span class='k-button-text'>Thêm</span></button>
    @*<button id='active' class='k-button k-button-md k-rounded-md k-button-solid k-button-solid-base' style='margin-right: 5px;'><span class='k-icon k-i-check k-button-icon'></span><span class='k-button-text'>Active</span></button>
        <button id='importExcel' class='k-button k-button-md k-rounded-md k-button-solid k-button-solid-base' style='margin-right: 5px;'><span class='k-icon k-i-file-excel k-button-icon'></span><span class='k-button-text'>Import Excel</span></button>*@
    </div>
</script>
<script type="text/javascript">
    let gridId = "#gridId";

    function renderCreateOrEditForm(isCreate = true, dataMaterial = {}) {
        let myWindow = $("#window");
        $("#window").html("<form id='formCreateAndEdit'></form>");

        let formData = {
            id: 0,
            name: "",
            name_EN: "",
            parent_ID: "",
            description: "",
            // status: "",
            ...dataMaterial
        };
        let strSubmit = "Thêm";
        let title = "THÊM MỚI"
        let element;
        if (isCreate == false) {
            strSubmit = "Sửa";
            title = "CẬP NHẬT";
        }
        $("#formCreateAndEdit").kendoForm({
            orientation: "vertical",
            formData: formData,
            type: "group",
            items: [
                {
                    field: "name",
                    title: "Nguyên liệu",
                    label: "Nguyên liệu (*):",
                    validation: {
                        validationMessage: "Vui lòng nhập nguyên liệu",
                        required: true
                    },
                },
                {
                    field: "name_EN",
                    title: "Nguyên liệu tiếng anh",
                    label: "Nguyên liệu tiếng anh:",
                },
                {
                    field: "parent_ID",
                    title: "Nguyên liệu cấp trên",
                    label: "Nguyên liệu cấp trên:",
                    editor: "DropDownList",
                    editorOptions: {
                        optionLabel: "Chọn nguyên liệu cấp trên",
                        dataTextField: "name",
                        dataValueField: "id",
                        //filter: "contains",
                        filter: filterCustom,
                        dataSource: {
                            transport: {
                                read: {
                                    url: "/Material/GetMaterialList",
                                    dataType: "json",
                                },
                                parameterMap: function (data, type) {
                                    if (type == "read") {

                                        // let filterStr = data?.filter?.filters[0]?.value || "";
                                        return {
                                            pageSize: data.pageSize,
                                            pageNumber: data.page,
                                            //searchString: filterStr
                                        }
                                    }
                                },
                            },
                            serverPaging: true,
                            serverFiltering: true,
                            page: 1,
                            pageSize: 9999,
                            schema: {
                                data: "data.data",
                                total: "data.total"
                            },
                        },
                    },
                    // validation: {
                    //     required: true
                    // },
                },
                {
                    field: "description",
                    title: "Mô tả",
                    label: "Mô tả:",
                    // validation: {
                    //     required: true
                    // },
                },

            ],
            messages: {
                submit: strSubmit, clear: "Đặt lại"
            },
            submit: function (e) {
                e.preventDefault();
                let dataItem = {
                    ...formData,
                    ...e.model,
                };
                if (dataItem.id > 0) {
                    var response = ajax("PUT", "/Material/UpdateMaterial/" + dataItem.id, dataItem, () => {
                        $(gridId).data("kendoGrid").dataSource.filter({});
                        myWindow.data("kendoWindow").close();
                    });
                }
                else {
                    var response = ajax("POST", "/Material/Create", dataItem, () => {
                        $(gridId).data("kendoGrid").dataSource.filter({});
                        myWindow.data("kendoWindow").close();
                    });
                }
            },
            close: function (e) {
                $(this.element).empty();
            },
        });
        if (!isCreate) {

        }

        // if (Userdata.roleIdList?.includes(ERoleType.Admin) == false) {
        //     $("#userName").data("kendoTextBox").enable(false);
        // }


        setTimeout(() => {
            $("input[title='name']").focus();
        }, 500);

        function remove() {
            setTimeout(() => {
                if ($(".k-window #window").length > 0) {
                    $("#window").parent().remove();
                    $(gridId).after("<div id='window'></div>");
                }
            }, 200)
        }

        myWindow.kendoWindow({
            width: "500px",
            // height: "50vh",
            title: "",
            visible: false,
            actions: ["Close"],
            resizable: false,
            draggable: false,
            modal: true,
            close: function (e) {
                //$("#window").empty();
                remove();
            },
        }).data("kendoWindow").title(title).center();
        myWindow.data("kendoWindow").open();
    }

    async function editMaterial(id) {
        var response = ajax("GET", "/Material/GetMaterialById/" + id, {}, (response) => {
            renderCreateOrEditForm(false, response.data);
        }, null, false);
    }
    function deleteMaterial(id) {
        $('#dialog').kendoConfirm({
            title: "THÔNG BÁO XÓA NGUYÊN LIỆU",
            content: "Bạn có chắc chắn xóa nguyên liệu này không?",
            size: "medium",
            messages: {
                okText: "Đồng ý",
                cancel: "Hủy"

            },
        }).data("kendoConfirm").open().result.done(function () {
            var response = ajax("DELETE", "/Material/DeleteMaterialById/" + id, {}, () => {
                $(gridId).data("kendoGrid").dataSource.filter({});
            });
        })

        $("#window").after("<div id='dialog'></div>");
    }



    async function ExportExcel() {
        let dataSheet1 = [
            {
                cells: [
                    { value: "Mã nguyên liệu", textAlign: "center", background: "#428dd8" },
                    { value: "Nguyên liệu", textAlign: "center", background: "#428dd8" },
                    { value: "Nguyên liệu tiếng anh", textAlign: "center", background: "#428dd8" },
                    { value: "Mô tả", textAlign: "center", background: "#428dd8" },
                    { value: "Ngày cập nhật", textAlign: "center", background: "#428dd8" },
                    { value: "Người cập nhật", textAlign: "center", background: "#428dd8" },
                ]
            }];

        var searchModel = getSearchModel();
        let postData = {
            ...searchModel,
            pageSize: 999999999,
            pageNumber: 1
        }
        let dataSourceMaterial = null;
        var response = await ajax("GET", "/Material/GetMaterialList", postData, (urnResponse) => {
            dataSourceMaterial = urnResponse.data.data;
        }, null, false);
        if (dataSourceMaterial == null) return;

        for (let index = 0; index < dataSourceMaterial.length; index++) {
            dataSheet1.push({
                cells: [
                    { value: dataSourceMaterial[index].code },
                    { value: dataSourceMaterial[index].name },
                    { value: dataSourceMaterial[index].name_EN },
                    { value: dataSourceMaterial[index].description },
                    { value: kendo.toString(kendo.parseDate(dataSourceMaterial[index].updatedDate), "dd/MM/yyyy") },
                    { value: dataSourceMaterial[index].updatedByName },
                ]
            })
        }

        var workbook = new kendo.ooxml.Workbook({
            sheets: [
                {
                    name: "Danh sách nguyên liệu",
                    columns: [
                        { width: 150 }, { width: 200 }, { width: 200 },
                        { width: 250 }, { autoWidth: true }, { autoWidth: true }
                    ],
                    rows: dataSheet1,
                }
            ]
        });
        kendo.saveAs({
            dataURI: workbook.toDataURL(),
            fileName: "Danh sách nguyên liệu _ " + kendo.toString(new Date(), "dd_MM_yyyy__HH_mm_ss") + ".xlsx"
        });
    }

    function getSearchModel() {
        let searchString = $("#searchString").val();

        return {
            searchString,
        };
    }
    function InitGrid() {
        let htmlToolbar = `
                <div id='toolbar' style=''  class='w-100 d-flex flex-column'>
                       <div class="row gx-0 row-gap-2 w-100">
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label for="searchString">Tìm kiếm:</label>
                                    <input type="text" class="w-100" id="searchString"/>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12 d-flex align-items-end">
                                <div class="pe-1 d-flex gap-2">
                                    <button id="search" title="Tìm kiếm" class="k-button k-button-md k-rounded-md k-button-solid k-button-solid-primary k-icon-button"><span class='k-icon k-i-search k-button-icon'></span><span class='k-button-text d-none'>Tìm kiếm</span></button>
                                    <button id='create' title="Thêm" class='k-button k-button-md k-rounded-md k-button-solid k-button-solid-success _permission_' data-enum='16'><span class='k-icon k-i-plus k-button-icon'></span><span class='k-button-text'>Thêm</span></button>
                                    <button id="exportExcel" class="k-button k-button-md k-rounded-md k-button-outline k-button-outline-error"><span class="k-icon k-i-file-excel k-button-icon"></span><span class="k-button-text">Export Excel</span></button>
                                </div>
                            </div>
                        </div>

                </div>
            `;

        $(gridId).kendoGrid({
            dataSource: {
                transport: {
                    read: {
                        url: "/Material/GetMaterialList",
                        datatype: "json",
                    },
                    parameterMap: function (data, type) {
                        if (type == "read") {
                            var searchModel = getSearchModel();
                            return {
                                ...searchModel,
                                pageSize: data.pageSize,
                                pageNumber: data.page
                            }
                        }

                    },
                },
                serverPaging: true,
                serverFiltering: true,
                page: 1,
                pageSize: 20,
                schema: {
                    type: 'json',
                    parse: function (response) {
                        if (response.isSuccess == false) {
                            showErrorMessages(response.errorMessageList);
                            return {
                                data: [],
                                total: 0
                            }
                        }
                        return response.data;
                    },
                    model: {
                        id: "id",
                        fields: {
                            createdDate: { type: "date" },
                            updatedDate: { type: "date" },
                            stt: { type: "number" },

                        }
                    },
                    data: "data",
                    total: "total"
                },
            },
            selectable: true,
            scrollable: { virtual: false },
            pageable: {
                pageSizes: [10, 20, 50],
            },
            dataBinding: function (e) {
                record = (this.dataSource._page - 1) * this.dataSource._pageSize;
            },
            toolbar: htmlToolbar,
            // toolbar: "<div id='toolbar' style='width:100%'></div><div class='report-toolbar'>\</div>",
            columns: [
                {
                    field: "",
                    title: "STT",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    template: "#: ++record #",
                    width: "80px"
                },
                {
                    field: "code",
                    title: "Mã nguyên liệu",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    width: "150px",
                },
                {
                    field: "name",
                    title: "Nguyên liệu",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    width: "200px",
                },
                {
                    field: "name_EN",
                    title: "Nguyên liệu tiếng anh",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    width: "180px",
                },
                {
                    field: "description",
                    title: "Mô tả",
                    width: "250px",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:left;" },
                },
                {
                    field: "updatedDate",
                    title: "Ngày cập nhật",
                    width: "150px",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    template: '#: kendo.toString(kendo.parseDate(updatedDate), "dd/MM/yyyy HH:mm:ss")#',
                },
                {
                    field: "updatedByName",
                    title: "Người cập nhật",
                    width: "150px",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                },
                {
                    field: "", title: "Thao tác", width: "120px", attributes: { style: "text-align: center;" },
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    template: function (dataItem) {
                        return '<div class="action-buttons">' +
                            '<button onclick="editMaterial(' + dataItem.id + ')" title="Chỉnh sửa" class="btn-action btn-edit _permission_" data-enum="8">' +
                            '<i class="fas fa-edit"></i>' +
                            '</button>' +
                            '<button onclick="deleteMaterial(' + dataItem.id + ')" title="Xoá" class="btn-action btn-delete _permission_" data-enum="9">' +
                            '<i class="fas fa-trash"></i>' +
                            '</button>' +
                            '</div>';
                    }
                }
            ],
            dataBound: function (e) {
                CheckPermission();
            }
        });


    }
    function InitKendoToolBar() {

        $("#search").kendoButton({
            icon: "search"
        });
        $("#search").click(async function (e) {
            var grid = $(gridId).data("kendoGrid");
            grid.dataSource.filter({});
        });
        $("#exportExcel").click(async function (e) {
            ExportExcel();
        });
        $("#searchString").kendoTextBox({
            icon: {
                type: "search",
                position: "end"  // Có thể là "start" hoặc "end"
            },
            placeholder: "Nhập từ khóa tìm kiếm..."
        });
        $("#create").kendoButton({
            icon: "plus"
        });

        $("#export").click(async function (e) {
            let grid = $(gridId).data("kendoGrid");
            grid.saveAsExcel();
        });


        $("#create").on('click', function () {
            renderCreateOrEditForm();
        });

    };

</script>
<script type="text/javascript">
    InitGrid();
    InitKendoToolBar();
    $(document).ready(function () {
        $(window).trigger("resize");

    });
</script>
<style>
    .k-form-buttons {
        justify-content: flex-end;
    }
</style>
