/* Kendo Grid Common Styles - Unified Design System */

/* Main Grid Styling */
.k-grid {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-top: 0;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    width: 100%;
    table-layout: auto;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

/* Ensure consistent table layout for both scrollable and non-scrollable */
.k-grid .k-grid-content table,
.k-grid .k-grid-header table {
    width: 100%;
    table-layout: fixed !important;
}

/* Fix column alignment issues with fixed widths */
.k-grid .k-grid-header-wrap table,
.k-grid .k-grid-content table {
    table-layout: fixed !important;
    width: 100% !important;
}

/* Ensure header and content columns have same width */
.k-grid .k-grid-header .k-header,
.k-grid .k-grid-content td {
    box-sizing: border-box !important;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Force full width for non-scrollable grids */
.k-grid:not(.k-grid-scrollable) {
    width: 100% !important;
}

.k-grid:not(.k-grid-scrollable) table {
    width: 100% !important;
    table-layout: auto;
}

.k-grid .k-grid-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6;
}

.k-grid .k-grid-header .k-header {
    background: transparent;
    border-color: #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 12px 8px;
    text-align: center;
}

/* Alternating row colors for both grid types */

.k-grid .k-grid-content td {
    padding: 10px 8px;
    border-color: #e9ecef;
    vertical-align: middle;
}

/* Currency and Number Formatting */
.currency {
    font-weight: 600;
    color: #198754 !important;
}

.number {
    font-weight: 500;
    color: #495057 !important;
}

/* Ensure currency and number styling works in grid cells */
.k-grid .k-grid-content td .currency {
    font-weight: 600;
    color: #198754 !important;
}

.k-grid .k-grid-content td .number {
    font-weight: 500;
    color: #495057 !important;
}

/* Override currency and number colors in selected rows for better readability */
.k-grid .k-state-selected .currency,
.k-grid .k-state-selected td .currency,
.k-grid tbody tr.k-state-selected .currency,
.k-grid tbody tr.k-state-selected td .currency {
    color: #ffffff !important;
}

.k-grid .k-state-selected .number,
.k-grid .k-state-selected td .number,
.k-grid tbody tr.k-state-selected .number,
.k-grid tbody tr.k-state-selected td .number {
    color: #ffffff !important;
}

/* Detail grid currency and number styling */
.k-grid .k-detail-row .k-grid .k-grid-content td .currency {
    font-weight: 600;
    color: #198754 !important;
}

.k-grid .k-detail-row .k-grid .k-grid-content td .number {
    font-weight: 500;
    color: #495057 !important;
}

/* Detail Grid Styling */
.k-grid .k-detail-row .k-grid {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.k-grid .k-detail-row .k-grid .k-grid-header {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-bottom: 1px solid #f1c40f;
}

.k-grid .k-detail-row .k-grid .k-grid-header .k-header {
    font-size: 13px;
    font-weight: 600;
    color: #856404;
    padding: 8px 6px;
}

.k-grid .k-detail-row .k-grid .k-grid-content td {
    padding: 8px 6px;
    font-size: 13px;
    border-color: #f8f9fa;
}

.k-grid .k-detail-row .k-grid .k-alt {
    background-color: #fffbf0;
}

/* Pagination Styling */
.k-grid .k-pager-wrap {
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 12px;
}

.k-grid .k-pager-wrap .k-pager-numbers .k-current-page {
    background: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

/* Status Badges */
.badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;
    text-align: center;
    white-space: nowrap;
}

.badge-secondary {
    background-color: #6c757d;
    color: white;
}

.badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.badge-success {
    background-color: #28a745;
    color: white;
}

.badge-info {
    background-color: #17a2b8;
    color: white;
}

.badge-danger {
    background-color: #dc3545;
    color: white;
}

/* Action Buttons */
.btn-action {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    margin: 0 2px;
}

.btn-edit {
    background: #ffc107;
    color: #212529;
}

.btn-delete {
    background: #dc3545;
    color: white;
}

.btn-cancel {
    background: #fd7e14;
    color: white;
}

.btn-invoice {
    background: #6c757d;
    color: white;
}

.btn-view {
    background: #17a2b8;
    color: white;
}

/* Dropdown Menu Styling */
.dropdown-menu {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    min-width: 160px;
    z-index: 10000 !important;
}

.dropdown-item {
    padding: 8px 16px;
    font-size: 14px;
    display: flex;
    align-items: center;
}

/* Fix dropdown visibility in Kendo Grid */
.k-grid .dropdown-menu {
    z-index: 10000 !important;
    position: absolute !important;
}

.k-grid .action-buttons .dropdown {
    position: relative;
}

/* Ensure dropdown shows above all grid content */
.k-grid .dropdown.show .dropdown-menu {
    z-index: 10001 !important;
    position: absolute !important;
}

/* Grid scrolling behavior - allow normal scrolling */
.k-grid .k-grid-content {
    overflow: auto !important;
}

/* Default grid height for better UX */
.k-grid {
    min-height: 400px;
}

/* Ensure grid content can scroll properly */
.k-grid .k-grid-content {
    max-height: 500px;
    overflow-y: auto !important;
    overflow-x: auto !important;
}

/* Fix scrollable grid column alignment */
.k-grid.k-grid-scrollable .k-grid-header-wrap,
.k-grid.k-grid-scrollable .k-grid-content {
    overflow-x: auto !important;
}

/* Ensure scrollable grid tables maintain fixed layout */
.k-grid.k-grid-scrollable .k-grid-header table,
.k-grid.k-grid-scrollable .k-grid-content table {
    table-layout: fixed !important;
    min-width: 100% !important;
}

/* Prevent column width changes on scroll */
.k-grid.k-grid-scrollable .k-grid-header .k-header,
.k-grid.k-grid-scrollable .k-grid-content td {
    min-width: inherit !important;
    max-width: inherit !important;
}

/* Grid Performance Optimizations */
.k-grid .k-virtual-scrollable-wrap {
    overflow-x: auto;
    overflow-y: auto;
}

/* Grid Loading States */
.k-grid .k-loading-mask {
    background: rgba(248, 249, 250, 0.95);
    backdrop-filter: blur(2px);
}

.k-grid .k-loading-text {
    color: #495057;
    font-weight: 500;
    font-size: 14px;
}

/* Grid Empty State */
.k-grid .k-grid-norecords {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
    font-style: italic;
}

/* Improved Scrollbar Styling */
.k-grid .k-grid-content::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.k-grid .k-grid-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.k-grid .k-grid-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

/* Responsive Design for Grid */
@media (max-width: 768px) {
    .k-grid .k-grid-header,
    .k-grid .k-grid-content {
        font-size: 12px;
    }

    .k-grid .k-grid-content td,
    .k-grid .k-grid-header .k-header {
        padding: 6px 4px;
    }

    .btn-action {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    /* Mobile-specific grid adjustments */
    .k-grid {
        border-radius: 6px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    }

    .k-grid .k-grid-header .k-header {
        padding: 8px 4px;
        font-size: 11px;
    }
}

@media (max-width: 576px) {
    .k-grid .k-grid-content td {
        padding: 4px 2px;
        font-size: 11px;
    }

    .btn-action {
        width: 24px;
        height: 24px;
        font-size: 10px;
        margin: 0 1px;
    }
}

/* Grid Utility Classes */
.grid-text-center {
    text-align: center !important;
}

.grid-text-left {
    text-align: left !important;
}

.grid-text-right {
    text-align: right !important;
}

.grid-nowrap {
    white-space: nowrap !important;
}

.grid-ellipsis {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 200px !important;
}

/* Additional fixes for column alignment */
.k-grid .k-grid-header-wrap {
    border-right: 0 !important;
}

.k-grid .k-grid-content {
    border-top: 0 !important;
}

/* Ensure consistent column borders */
.k-grid .k-grid-header .k-header,
.k-grid .k-grid-content td {
    border-right: 1px solid #e9ecef !important;
    border-left: 0 !important;
}

.k-grid .k-grid-header .k-header:last-child,
.k-grid .k-grid-content td:last-child {
    border-right: 0 !important;
}

/* Force consistent width calculation */
.k-grid .k-grid-header colgroup col,
.k-grid .k-grid-content colgroup col {
    width: auto !important;
}

/* Grid Column Width Utilities */
.grid-col-narrow {
    width: 80px !important;
    min-width: 80px !important;
}

.grid-col-small {
    width: 120px !important;
    min-width: 120px !important;
}

.grid-col-medium {
    width: 150px !important;
    min-width: 150px !important;
}

.grid-col-large {
    width: 200px !important;
    min-width: 200px !important;
}

.grid-col-wide {
    width: 250px !important;
    min-width: 250px !important;
}

/* Grid Action Column Styling */
.action-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    flex-wrap: wrap;
}

.action-buttons .btn-action {
    flex-shrink: 0;
}

/* Grid Status Indicators */
.status-active {
    color: #28a745 !important;
    font-weight: 600;
}

.status-inactive {
    color: #dc3545 !important;
    font-weight: 600;
}

.status-pending {
    color: #ffc107 !important;
    font-weight: 600;
}

.status-processing {
    color: #17a2b8 !important;
    font-weight: 600;
}

/* Override status colors in selected rows for better readability */
.k-grid .k-state-selected .status-active,
.k-grid .k-state-selected .status-inactive,
.k-grid .k-state-selected .status-pending,
.k-grid .k-state-selected .status-processing,
.k-grid tbody tr.k-state-selected .status-active,
.k-grid tbody tr.k-state-selected .status-inactive,
.k-grid tbody tr.k-state-selected .status-pending,
.k-grid tbody tr.k-state-selected .status-processing {
    color: #ffffff !important;
}

/* Ensure all text in selected rows is white for better contrast */
.k-grid .k-state-selected,
.k-grid .k-state-selected td,
.k-grid tbody tr.k-state-selected,
.k-grid tbody tr.k-state-selected td {
    color: #ffffff !important;
}

/* Override any specific text colors in selected rows */
.k-grid .k-state-selected *,
.k-grid tbody tr.k-state-selected * {
    color: #ffffff !important;
}

/* Grid Toolbar Integration */
.k-grid .k-grid-toolbar {
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
    padding: 0;
}

.k-grid .k-grid-toolbar #toolbar {
    background: transparent;
    border: none;
    box-shadow: none;
    margin: 0;
    padding: 15px;
}

/* Print Styles */
@media print {
    .k-grid {
        border: 1px solid #000;
        box-shadow: none;
    }

    .k-grid .k-grid-header {
        background: #f0f0f0 !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .btn-action,
    .action-buttons {
        display: none !important;
    }

    .k-grid .k-pager-wrap {
        display: none !important;
    }
}
