﻿@{
    ViewData["Title"] = "Khách hàng";
}

@section Styles {
    <link href="~/css/kendo-grid-common.css" rel="stylesheet" />
    <link href="~/css/toolbar-common.css" rel="stylesheet" />
}

<div>

    <h4 class="demo-section wide title">@ViewData["Title"]</h4>
    @*   <div class="demo-section wide title">
    <h4 class="demo-section wide title">@ViewData["Title"]</h4>
    <nav id="breadcrumb"></nav>
    </div> *@
    <div id="divContent">
        <div id="gridId">
        </div>
    </div>
    <div id="window"></div>
    <div id="dialog"></div>
</div>

<script type="text/javascript">
    let gridId = "#gridId";
    var customerTypeDatasource = @Html.Raw(Json.Serialize(@ViewBag.CustomerTypeDatasource));

    function renderCreateOrEditForm(isCreate = true, dataCustomer = {}) {
        let myWindow = $("#window");
        $("#window").html("<form id='formCreateAndEdit'></form>");

        let formData = {
            id: 0,
            name: "",
            type: "",
            phoneContact: null,
            email: null,
            taxCode: null,
            address: null,
            ...dataCustomer
        };
        let strSubmit = "Thêm";
        let title = "THÊM MỚI"
        let element;
        if (isCreate == false) {
            strSubmit = "Sửa";
            title = "CẬP NHẬT";
        }
        $("#formCreateAndEdit").kendoForm({
            orientation: "vertical",
            formData: formData,
            type: "group",
            items: [
                {
                    field: "name",
                    title: "Họ tên",
                    label: "Họ tên (*):",
                    validation: {
                        validationMessage: "Vui lòng nhập họ tên",
                        required: true
                    },
                },
                {
                    field: "type",
                    title: "Loại tài khoản",
                    label: "Loại tài khoản (*):",
                    editor: "DropDownList",
                    editorOptions: {
                        optionLabel: "Chọn loại tài khoản",
                        dataTextField: "text",
                        dataValueField: "value",
                        filter: filterCustom,
                        dataSource: customerTypeDatasource,
                    },
                    validation: {
                        validationMessage: "Vui lòng chọn loại tài khoản",
                        required: true
                    },
                },
                {
                    field: "phoneContact",
                    title: "Số điện thoại",
                    label: "Số điện thoại:",
                    // validation: {
                    //     validationMessage: "Vui lòng nhập số điện thoại",
                    //     required: true
                    // },
                },
                {
                    field: "email",
                    title: "Email",
                    label: "Email:",
                    // validation: {
                    //     validationMessage: "Vui lòng nhập email",
                    //     required: true
                    // },
                },
                {
                    field: "taxCode",
                    title: "Mã số thuế",
                    label: "Mã số thuế:",
                    // validation: {
                    //     validationMessage: "Vui lòng nhập Mã số thuế",
                    //     required: true
                    // },
                },
                {
                    field: "address",
                    title: "Địa chỉ",
                    label: "Địa chỉ:",
                    validation: {
                        address: true
                    },
                },
            ],
            messages: {
                submit: strSubmit, clear: "Đặt lại"
            },
            submit: function (e) {
                e.preventDefault();
                let dataItem = {
                    ...formData,
                    ...e.model,
                };
                if (dataItem.id > 0) {
                    var response = ajax("PUT", "/Customer/UpdateCustomer/" + dataItem.id, dataItem, () => {
                        $(gridId).data("kendoGrid").dataSource.filter({});
                        myWindow.data("kendoWindow").close();
                    });
                }
                else {
                    var response = ajax("POST", "/Customer/Create", dataItem, () => {
                        $(gridId).data("kendoGrid").dataSource.filter({});
                        myWindow.data("kendoWindow").close();
                    });
                }

            },
            close: function (e) {
                $(this.element).empty();
            },
        });
        if (!isCreate) {

        }

        // if (Userdata.roleIdList?.includes(ERoleType.Admin) == false) {
        //     $("#userName").data("kendoTextBox").enable(false);
        // }


        setTimeout(() => {
            $("input[title='name']").focus();
        }, 500);

        function remove() {
            setTimeout(() => {
                if ($(".k-window #window").length > 0) {
                    $("#window").parent().remove();
                    $(gridId).after("<div id='window'></div>");
                }
            }, 200)
        }

        myWindow.kendoWindow({
            width: "500px",
            // height: "50vh",
            title: "",
            visible: false,
            actions: ["Close"],
            resizable: false,
            draggable: false,
            modal: true,
            close: function (e) {
                //$("#window").empty();
                remove();
            },
        }).data("kendoWindow").title(title).center();
        myWindow.data("kendoWindow").open();
    }

    async function editCustomer(id) {
        var response = ajax("GET", "/Customer/GetCustomerById/" + id, {}, (response) => {
            renderCreateOrEditForm(false, response.data);
        }, null, false);
    }
    function deleteCustomer(id) {
        $('#dialog').kendoConfirm({
            title: "THÔNG BÁO XÓA KHÁCH HÀNG",
            content: "Bạn có chắc chắn xóa khách hàng này không?",
            size: "medium",
            messages: {
                okText: "Đồng ý",
                cancel: "Hủy"

            },
        }).data("kendoConfirm").open().result.done(function () {
            var response = ajax("DELETE", "/Customer/DeleteCustomerById/" + id, {}, () => {
                $(gridId).data("kendoGrid").dataSource.filter({});
            });
        })

        $("#window").after("<div id='dialog'></div>");
    }


    async function ExportExcel() {
        let dataSheet1 = [
            {
                cells: [
                    {
                        value: "Mã khách hàng", textAlign: "center", background: "#428dd8"
                    },
                    {
                        value: "Họ tên", textAlign: "center", background: "#428dd8"
                    },
                    {
                        value: "Loại khách hàng", textAlign: "center", background: "#428dd8"
                    },
                    {
                        value: "Số điện thoại", textAlign: "center", background: "#428dd8"
                    },
                    {
                        value: "Email", textAlign: "center", background: "#428dd8"
                    },
                    {
                        value: "Mã số thuế", textAlign: "center", background: "#428dd8"
                    },
                    {
                        value: "Địa chỉ", textAlign: "center", background: "#428dd8"
                    },
                ]
            }];

        var searchModel = getSearchModel();
        let postData = {
            ...searchModel,
            pageSize: 999999999,
            pageNumber: 1
        }
        let dataSourceCustomer = null;
        var response = await ajax("GET", "/Customer/GetCustomerList", postData, (urnResponse) => {
            dataSourceCustomer = urnResponse.data.data;
        }, null, false);
        if (dataSourceCustomer == null) return;

        for (let index = 0; index < dataSourceCustomer.length; index++) {
            dataSheet1.push({
                cells: [
                    { value: dataSourceCustomer[index].code },
                    { value: dataSourceCustomer[index].name },
                    { value: dataSourceCustomer[index].typeName },
                    { value: dataSourceCustomer[index].phone },
                    { value: dataSourceCustomer[index].email },
                    { value: dataSourceCustomer[index].taxCode },
                    { value: dataSourceCustomer[index].address },
                ]
            })
        }


        var workbook = new kendo.ooxml.Workbook({
            sheets: [
                {
                    name: "Danh sách khách hàng",
                    columns: [
                        { width: 200 }, { autoWidth: true }, { autoWidth: true },
                        { autoWidth: true }, { autoWidth: true }, { autoWidth: true }
                    ],
                    rows: dataSheet1,
                }
            ]
        });
        kendo.saveAs({
            dataURI: workbook.toDataURL(),
            fileName: "Danh sách khách hàng _ " + kendo.toString(new Date(), "dd_MM_yyyy__HH_mm_ss") + ".xlsx"
        });
    }

    function getSearchModel() {
        let searchString = $("#searchString").val();

        return {
            searchString,
        };
    }
    function InitGrid() {
        let htmlToolbar = `
                <div id='toolbar' style=''  class='w-100 d-flex flex-column'>
                       <div class="row gx-0 row-gap-2 w-100">
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label for="searchString">Tìm kiếm:</label>
                                    <input type="text" class="w-100" id="searchString"/>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12 d-flex align-items-end">
                                <div class="pe-1 d-flex gap-2">
                                    <button id="search" title="Tìm kiếm" class="k-button k-button-md k-rounded-md k-button-solid k-button-solid-primary k-icon-button"><span class='k-icon k-i-search k-button-icon'></span><span class='k-button-text d-none'>Tìm kiếm</span></button>
                                    <button id='create' title="Thêm" class='k-button k-button-md k-rounded-md k-button-solid k-button-solid-success _permission_' data-enum='8'><span class='k-icon k-i-plus k-button-icon'></span><span class='k-button-text'>Thêm</span></button>
                                    <button id="downloadTemplate" title="Tải mẫu Excel" class="k-button k-button-md k-rounded-md k-button-outline k-button-outline-info"><span class="k-icon k-i-download k-button-icon"></span><span class="k-button-text">Tải mẫu</span></button>
                                    <button id="importExcel" title="Import Excel" class="k-button k-button-md k-rounded-md k-button-outline k-button-outline-warning _permission_" data-enum="16"><span class="k-icon k-i-upload k-button-icon"></span><span class="k-button-text">Import Excel</span></button>
                                    <button id="exportExcel" class="k-button k-button-md k-rounded-md k-button-outline k-button-outline-error"><span class="k-icon k-i-file-excel k-button-icon"></span><span class="k-button-text">Export Excel</span></button>
                                </div>
                            </div>
                        </div>
                </div>
            `;

        $(gridId).kendoGrid({
            dataSource: {
                transport: {
                    read: {
                        url: "/Customer/GetCustomerList",
                        datatype: "json",
                    },
                    parameterMap: function (data, type) {
                        if (type == "read") {
                            var searchModel = getSearchModel();
                            return {
                                ...searchModel,
                                pageSize: data.pageSize,
                                pageNumber: data.page
                            }
                        }

                    },
                },
                serverPaging: true,
                serverFiltering: true,
                page: 1,
                pageSize: 20,
                schema: {
                    type: 'json',
                    parse: function (response) {
                        if (response.isSuccess == false) {
                            showErrorMessages(response.errorMessageList);
                            return {
                                data: [],
                                total: 0
                            }
                        }
                        return response.data;
                    },
                    model: {
                        id: "id",
                        fields: {
                            createdDate: { type: "date" },
                            updatedDate: { type: "date" },
                            stt: { type: "number" },

                        }
                    },
                    data: "data",
                    total: "total"
                },
            },
            selectable: true,
            scrollable: { virtual: false },
            pageable: {
                pageSizes: [10, 20, 50],
            },
            dataBinding: function (e) {
                record = (this.dataSource._page - 1) * this.dataSource._pageSize;
            },
            toolbar: htmlToolbar,
            // toolbar: "<div id='toolbar' style='width:100%'></div><div class='report-toolbar'>\</div>",
            columns: [
                {
                    field: "",
                    title: "STT",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    template: "#: ++record #",
                    width: "80px"
                },
                {
                    field: "code",
                    title: "Mã khách hàng",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    width: "150px",
                },
                {
                    field: "name",
                    title: "Họ tên",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    width: "180px",
                },
                {
                    field: "typeName",
                    title: "Loại khách hàng",
                    width: "140px",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                },
                {
                    field: "phone",
                    title: "Số điện thoại",
                    width: "130px",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                },
                {
                    field: "email",
                    title: "Email",
                    width: "200px",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                },
                {
                    field: "taxCode",
                    title: "Mã số thuế",
                    width: "120px",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                },
                {
                    field: "address",
                    title: "Địa chỉ",
                    width: "250px",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:left;" },
                },
                {
                    field: "updatedDate",
                    title: "Ngày cập nhật",
                    width: "150px",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    template: '#: kendo.toString(kendo.parseDate(updatedDate || createdDate), "dd/MM/yyyy HH:mm:ss")#',
                },
                {
                    field: "updatedByName",
                    title: "Người cập nhật",
                    width: "150px",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                },
                {
                    field: "", title: "Thao tác", width: "120px", attributes: { style: "text-align: center;" },
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    template: function (dataItem) {
                        return '<div class="action-buttons">' +
                            '<button onclick="editCustomer(' + dataItem.id + ')" title="Chỉnh sửa" class="btn-action btn-edit _permission_" data-enum="10">' +
                            '<i class="fas fa-edit"></i>' +
                            '</button>' +
                            '<button onclick="deleteCustomer(' + dataItem.id + ')" title="Xoá" class="btn-action btn-delete _permission_" data-enum="11">' +
                            '<i class="fas fa-trash"></i>' +
                            '</button>' +
                            '</div>';
                    }
                }
            ],
            dataBound: function (e) {
                CheckPermission();
            }
        });


    }
    function InitKendoToolBar() {

        $("#search").kendoButton({
            icon: "search"
        });
        $("#search").click(async function (e) {
            var grid = $(gridId).data("kendoGrid");
            grid.dataSource.filter({});
        });
        $("#exportExcel").click(async function (e) {
            ExportExcel();
        });
        $("#importExcel").click(async function (e) {
            showImportDialog();
        });
        $("#downloadTemplate").click(async function (e) {
            downloadTemplate();
        });
        $("#searchString").kendoTextBox({
            icon: {
                type: "search",
                position: "end"  // Có thể là "start" hoặc "end"
            },
            placeholder: "Nhập từ khóa tìm kiếm..."
        });
        $("#create").kendoButton({
            icon: "plus"
        });

        $("#export").click(async function (e) {
            let grid = $(gridId).data("kendoGrid");
            grid.saveAsExcel();
        });


        $("#create").on('click', function () {
            renderCreateOrEditForm();
        });

    };

    // Download Excel template function
    async function downloadTemplate() {
        try {
            const response = await fetch('/Customer/DownloadTemplate');
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `Customer_Import_Template_${new Date().toISOString().slice(0, 10)}.xlsx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                notification.show({
                    title: "Thành công!",
                    message: "Đã tải xuống file mẫu thành công!"
                }, "success");
            } else {
                throw new Error('Không thể tải xuống file mẫu');
            }
        } catch (error) {
            console.error('Error downloading template:', error);
            notification.show({
                title: "Lỗi!",
                message: "Lỗi khi tải xuống file mẫu: " + error.message
            }, "error");
        }
    }

    // Show import dialog function
    function showImportDialog() {
        const dialogHtml = `
            <div id="importDialog">
                <div class="k-form-layout" style="grid-template-columns: 1fr;">
                    <div class="k-form-field">
                        <label class="k-label k-form-label">Chọn file Excel để import:</label>
                        <div class="k-form-field-wrap">
                            <input type="file" id="importFile" accept=".xlsx,.xls,.xlsm,.csv" class="k-textbox" />
                            <div class="k-form-hint">Chỉ chấp nhận file Excel (.xlsx, .xls, .xlsm) hoặc CSV</div>
                        </div>
                    </div>
                    <div class="k-form-field">
                        <div class="k-form-field-wrap">
                            <div id="importProgress" style="display: none;">
                                <div class="k-progressbar">
                                    <div class="k-progress-status-wrap">
                                        <span class="k-progress-status">Đang xử lý...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        const dialog = $(dialogHtml).kendoDialog({
            width: "500px",
            title: "Import khách hàng từ Excel",
            closable: true,
            modal: true,
            actions: [
                {
                    text: "Hủy",
                    action: function (e) {
                        return true; // Close dialog
                    }
                },
                {
                    text: "Import",
                    primary: true,
                    action: function (e) {
                        const fileInput = document.getElementById('importFile');
                        if (fileInput.files.length === 0) {
                            notification.show({
                                title: "Cảnh báo!",
                                message: "Vui lòng chọn file để import!"
                            }, "info");
                            return false; // Don't close dialog
                        }

                        importExcelFile(fileInput.files[0]);
                        return false; // Don't close dialog yet
                    }
                }
            ]
        }).data("kendoDialog");

        dialog.open();
    }

    // Import Excel file function
    async function importExcelFile(file) {
        try {
            $("#importProgress").show();

            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch('/Customer/ImportExcel', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            $("#importProgress").hide();

            if (result.isSuccess && result.data) {
                showImportResult(result.data);

                // Refresh grid if there were successful imports
                if (result.data.successfulRows > 0) {
                    $(gridId).data("kendoGrid").dataSource.read();
                }
            } else {
                const errorMessage = result.errorMessageList && result.errorMessageList.length > 0
                    ? result.errorMessageList.join('<br/>')
                    : 'Có lỗi xảy ra khi import file';
                notification.show({
                    title: "Lỗi!",
                    message: errorMessage
                }, "error");
            }

            // Close import dialog
            $(".k-dialog").each(function () {
                const dialog = $(this).data("kendoDialog");
                if (dialog) {
                    dialog.close();
                }
            });

        } catch (error) {
            $("#importProgress").hide();
            console.error('Error importing file:', error);
            notification.show({
                title: "Lỗi!",
                message: "Lỗi khi import file: " + error.message
            }, "error");
        }
    }

    // Show import result function
    function showImportResult(importResult) {
        let resultHtml = `
            <div class="import-result">
                <h4>Kết quả Import</h4>
                <div class="result-summary">
                    <p><strong>Tổng số dòng xử lý:</strong> ${importResult.totalRows}</p>
                    <p><strong>Thành công:</strong> <span class="text-success">${importResult.successfulRows}</span></p>
                    <p><strong>Thất bại:</strong> <span class="text-danger">${importResult.failedRows}</span></p>
                </div>
        `;

        if (importResult.errors && importResult.errors.length > 0) {
            resultHtml += `
                <div class="error-details">
                    <h5>Chi tiết lỗi:</h5>
                    <div class="error-list" style="max-height: 300px; overflow-y: auto;">
            `;

            importResult.errors.forEach(error => {
                resultHtml += `
                    <div class="error-item" style="margin-bottom: 10px; padding: 10px; border-left: 3px solid #dc3545; background-color: #f8f9fa;">
                        <strong>Dòng ${error.rowNumber}:</strong> ${error.customerName || 'N/A'}<br/>
                        <span class="text-danger">${error.errorMessages.join(', ')}</span>
                    </div>
                `;
            });

            resultHtml += `
                    </div>
                </div>
            `;
        }

        resultHtml += `</div>`;

        const resultDialog = $(resultHtml).kendoDialog({
            width: "600px",
            title: "Kết quả Import Excel",
            closable: true,
            modal: true,
            actions: [
                {
                    text: "Đóng",
                    action: function (e) {
                        return true; // Close dialog
                    }
                }
            ]
        }).data("kendoDialog");

        resultDialog.open();

        // Show notification
        if (importResult.failedRows === 0) {
            notification.show({
                title: "Thành công!",
                message: `Import thành công ${importResult.successfulRows} khách hàng!`
            }, "success");
        } else {
            notification.show({
                title: "Hoàn tất!",
                message: `Import hoàn tất với ${importResult.successfulRows} thành công và ${importResult.failedRows} lỗi`
            }, "info");
        }
    }

</script>
<script type="text/javascript">
    InitGrid();
    InitKendoToolBar();
    $(document).ready(function () {
        $(window).trigger("resize");

    });
</script>
<style>
    .k-form-buttons {
        justify-content: flex-end;
    }

    /* Import dialog styles */
    .import-result {
        padding: 15px;
    }

    .result-summary {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 15px;
    }

    .result-summary p {
        margin: 5px 0;
    }

    .error-details {
        margin-top: 15px;
    }

    .error-list {
        background-color: #fff;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 10px;
    }

    .error-item {
        border-left: 3px solid #dc3545 !important;
        background-color: #f8f9fa !important;
        margin-bottom: 10px !important;
        padding: 10px !important;
        border-radius: 3px;
    }

    .text-success {
        color: #28a745 !important;
    }

    .text-danger {
        color: #dc3545 !important;
    }

    .text-warning {
        color: #ffc107 !important;
    }

    /* Import progress styles */
    #importProgress {
        text-align: center;
        padding: 20px;
    }

    .k-progressbar {
        margin: 10px 0;
    }
</style>
