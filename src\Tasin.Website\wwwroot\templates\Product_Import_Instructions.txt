HƯỚNG DẪN IMPORT SẢN PHẨM

1. <PERSON><PERSON><PERSON> cột bắt buộc:
   - <PERSON><PERSON><PERSON> sản phẩm (*): <PERSON><PERSON><PERSON><PERSON> được để trống
   - Thu<PERSON> suất công ty (%): B<PERSON><PERSON> buộc nhập số
   - <PERSON>hu<PERSON> suất người tiêu dùng (%): <PERSON><PERSON><PERSON> buộc nhập số

2. <PERSON><PERSON><PERSON> cột tùy chọn:
   - Tên tiếng Anh: <PERSON><PERSON> thể để trống
   - Mã đơn vị: Phải tồn tại trong hệ thống (ví dụ: KG, THUNG, CAI)
   - Mã quy cách: <PERSON><PERSON>i tồn tại trong hệ thống
   - Mã phân loại: <PERSON><PERSON><PERSON> tồn tại trong hệ thống
   - Là nguyên liệu: Nhập Y/N, Yes/No, True/False, 1/0
   - <PERSON><PERSON> thuế suất đặc biệt: <PERSON><PERSON><PERSON> tồn tại trong hệ thống
   - <PERSON><PERSON><PERSON> tỷ lệ %: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> thập phân (ví dụ: 10.5)
   - <PERSON><PERSON> chế biến: <PERSON><PERSON><PERSON><PERSON> số
   - Đơn giá mặc định: <PERSON><PERSON><PERSON><PERSON> số (ví dụ: 50000)
   - <PERSON>hi chú: Có thể để trống
   - Ngừng sản xuất: Nhập Y/N, Yes/No, True/False, 1/0

3. Lưu ý:
   - Không được xóa dòng tiêu đề
   - Dữ liệu bắt đầu từ dòng 2
   - File phải có định dạng Excel (.xlsx, .xls, .xlsm) hoặc CSV
   - Mã các thực thể liên quan phải tồn tại trong hệ thống
   - Xóa dữ liệu mẫu trước khi nhập dữ liệu thực tế

4. Các mã thường dùng:
   - Đơn vị: KG, THUNG, CAI, GOI, HOP
   - Quy cách: CAT001, CAT002, CAT003
   - Phân loại: PT001, PT002, PT003

5. Quy trình import:
   - Tải file mẫu từ hệ thống
   - Điền dữ liệu vào file mẫu
   - Kiểm tra dữ liệu trước khi import
   - Upload file và xem kết quả import
   - Kiểm tra các lỗi nếu có và sửa chữa

6. Xử lý lỗi:
   - Nếu có lỗi, hệ thống sẽ hiển thị chi tiết lỗi theo từng dòng
   - Sửa lỗi trong file Excel và import lại
   - Chỉ những dòng không có lỗi mới được import thành công
